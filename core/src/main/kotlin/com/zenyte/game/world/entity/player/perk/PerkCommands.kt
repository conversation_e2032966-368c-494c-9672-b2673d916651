package com.zenyte.game.world.entity.player.perk

import com.near_reality.game.content.shop.ShopCurrencyHandler
import com.near_reality.game.world.entity.player.combatEssence
import com.near_reality.game.world.entity.player.utilityEssence
import com.zenyte.game.content.essence.combat.CombatPerkWrapper
import com.zenyte.game.content.essence.utility.UtilityPerkWrapper
import com.zenyte.game.model.shop.ShopCurrency
import com.zenyte.game.world.World
import com.zenyte.game.world.entity.player.GameCommands
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege
import com.zenyte.plugins.dialogue.OptionsMenuD
import kotlin.jvm.optionals.getOrNull

object PerkCommands {
    fun register() {
        GameCommands.Command(PlayerPrivilege.DEVELOPER, "takeutilityperk") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.dialogueManager.start(object : OptionsMenuD(
                        player, "Select a Utility Perk to Remove",
                        *UtilityPerkWrapper.entries.map { it.perk.simpleName }.toTypedArray()

                    ) {
                        override fun handleClick(slotId: Int) {
                            val perk = UtilityPerkWrapper.PERKS_BY_ID.getOrDefault(slotId + 1, UtilityPerkWrapper.Unknown)
                            if(perk != UtilityPerkWrapper.Unknown) {
                                targetPlayer.utilityPerkManager.unlockedPerks.remove(perk.perk.simpleName)
                                player.sendMessage("Removed unlocked utility perk: ${perk.perk.simpleName} from ${targetPlayer.username}")
                            } else {
                                player.sendMessage("Unknown perk selected. Please contact management with the perk selected.")
                            }
                        }
                    })
                }
            }
        }

        GameCommands.Command(PlayerPrivilege.DEVELOPER, "giveutilityperk") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.dialogueManager.start(object : OptionsMenuD(
                        player, "Select a Utility Perk to Add",
                        *UtilityPerkWrapper.entries.map { it.perk.simpleName }.toTypedArray()

                    ) {
                        override fun handleClick(slotId: Int) {
                            val perk = UtilityPerkWrapper.PERKS_BY_ID.getOrDefault(slotId + 1, UtilityPerkWrapper.Unknown)
                            if(perk != UtilityPerkWrapper.Unknown) {
                                targetPlayer.utilityPerkManager.unlockedPerks.add(perk.perk.simpleName)
                                player.sendMessage("Added unlocked utility perk: ${perk.perk.simpleName} to ${targetPlayer.username}")
                            } else {
                                player.sendMessage("Unknown perk selected. Please contact management with the perk selected.")
                            }
                        }
                    })
                }
            }
        }

        GameCommands.Command(PlayerPrivilege.DEVELOPER, "setutilityessence") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.sendInputInt("Enter utility essence to set to: (current: ${ShopCurrencyHandler.getAmount(ShopCurrency.UTILITY_ESSENCE, player)})"){
                        player.utilityEssence = it
                        player.sendMessage("Set utility essence of player ${targetPlayer.username} to $it")
                    }
                }
            }
        }


        GameCommands.Command(PlayerPrivilege.DEVELOPER, "takecombatperk") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.dialogueManager.start(object : OptionsMenuD(
                        player, "Select a Combat Perk to Remove",
                        *CombatPerkWrapper.entries.map { it.perk.simpleName }.toTypedArray()

                    ) {
                        override fun handleClick(slotId: Int) {
                            val perk = CombatPerkWrapper.PERKS_BY_ID.getOrDefault(slotId + 1, CombatPerkWrapper.Unknown)
                            if(perk != CombatPerkWrapper.Unknown) {
                                targetPlayer.combatPerkManager.unlockedPerks.remove(perk.perk.simpleName)
                                player.sendMessage("Removed unlocked combat perk: ${perk.perk.simpleName} from ${targetPlayer.username}")
                            } else {
                                player.sendMessage("Unknown perk selected. Please contact management with the perk selected.")
                            }
                        }
                    })
                }
            }
        }

        GameCommands.Command(PlayerPrivilege.DEVELOPER, "givecombatperk") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.dialogueManager.start(object : OptionsMenuD(
                        player, "Select a Combat Perk to Add",
                        *CombatPerkWrapper.entries.map { it.perk.simpleName }.toTypedArray()

                    ) {
                        override fun handleClick(slotId: Int) {
                            val perk = CombatPerkWrapper.PERKS_BY_ID.getOrDefault(slotId + 1, CombatPerkWrapper.Unknown)
                            if(perk != CombatPerkWrapper.Unknown) {
                                targetPlayer.combatPerkManager.unlockedPerks.add(perk.perk.simpleName)
                                player.sendMessage("Added unlocked combat perk: ${perk.perk.simpleName} to ${targetPlayer.username}")
                            } else {
                                player.sendMessage("Unknown perk selected. Please contact management with the perk selected.")
                            }
                        }
                    })
                }
            }
        }

        GameCommands.Command(PlayerPrivilege.DEVELOPER, "setcombatessence") { player, _ ->
            player.sendInputName("Enter target player") { botName ->
                val targetPlayer = World.getPlayer(botName).getOrNull()
                if (targetPlayer == null)
                    player.dialogue { plain("No player online found by name `$botName`") }
                else {
                    player.sendInputInt("Enter combat essence to set to: (current: ${ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player)})"){
                        player.combatEssence = it
                        player.sendMessage("Set combat essence of player ${targetPlayer.username} to $it")
                    }
                }
            }
        }
    }
}