package com.zenyte.plugins.renewednpc;

import com.near_reality.api.service.vote.VotePlayerAttributesKt;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.Player;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> | 16-12-2018 | 20:58
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public class HomeShopNPC extends NPCPlugin {

    private static final Map<Integer, String> SHOPS = new HashMap<>() {

        {
            put(NpcId.TRISTAN, "Melee Store");
            put(NpcId.FAE, "Ranged Store");
            put(NpcId.BABA_YAGA, "Magic Store");
            put(NpcId.JOHN_16007, "Consumables Store");
            put(NpcId.HERQUIN, "Tools Store");
        }
    };

    @Override
    public void handle() {
        bind("Trade", new OptionHandler() {

            @Override
            public void handle(Player player, NPC npc) {
                player.stopAll();
                player.faceEntity(npc);
                if (npc.getId() == NpcId.TRISTAN && player.getNumericAttribute("demon_kills").intValue() == 100) {
                    player.openShop("Melee Store<Alternative>");
                } else {
                    player.openShop(SHOPS.get(npc.getId()));
                }
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                handle(player, npc);
            }
        });
        bind("Vote Store", new OptionHandler() {

            @Override
            public void handle(final Player player, final NPC npc) {
                player.openShop("Vote Shop");
                player.sendMessage("You currently have " + Colour.RED.wrap(VotePlayerAttributesKt.getTotalVotePoints(player)) + " vote points.");
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
            }
        });
        bind("Loyalty Store", new OptionHandler() {

            @Override
            public void handle(final Player player, final NPC npc) {
                player.openShop("Loyalty Shop");
                player.sendMessage("You currently have " + Colour.RED.wrap(player.getLoyaltyManager().getLoyaltyPoints()) + " loyalty points.");
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
            }
        });
    }

    @Override
    public int[] getNPCs() {
        return new int[] { NpcId.TRISTAN, NpcId.FAE, NpcId.BABA_YAGA, NpcId.JOHN_16007, NpcId.HERQUIN, NpcId.KINGS_MESSENGER };
    }
}
